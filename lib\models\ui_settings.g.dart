// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ui_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UISettings _$UISettingsFromJson(Map<String, dynamic> json) => _UISettings(
  id: (json['id'] as num?)?.toInt(),
  portraitColumns: (json['portraitColumns'] as num?)?.toInt() ?? 4,
  landscapeColumns: (json['landscapeColumns'] as num?)?.toInt() ?? 8,
  showProductImages: json['showProductImages'] as bool? ?? true,
  showProductDescriptions: json['showProductDescriptions'] as bool? ?? true,
  compactMode: json['compactMode'] as bool? ?? false,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  syncMetadata: json['syncMetadata'] == null
      ? null
      : SyncMetadata.fromJson(json['syncMetadata'] as Map<String, dynamic>),
);

Map<String, dynamic> _$UISettingsToJson(_UISettings instance) =>
    <String, dynamic>{
      'id': instance.id,
      'portraitColumns': instance.portraitColumns,
      'landscapeColumns': instance.landscapeColumns,
      'showProductImages': instance.showProductImages,
      'showProductDescriptions': instance.showProductDescriptions,
      'compactMode': instance.compactMode,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'syncMetadata': instance.syncMetadata,
    };
