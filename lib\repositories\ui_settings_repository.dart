/// Blue Booth Manager - UI 설정 Repository
///
/// UI 설정 데이터의 CRUD 작업을 담당하는 Repository 클래스입니다.
/// - 스마트폰/타블렛 별도 열 수 설정 관리
/// - SQLite 로컬 저장소 연동
/// - 실시간 동기화 지원
/// - 트랜잭션 안전성 보장
///
/// 주요 기능:
/// - UI 설정 생성, 조회, 수정, 삭제
/// - 디바이스별 열 수 설정 관리
/// - 동기화 메타데이터 처리
/// - SQL 인젝션 방지
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:sqflite/sqflite.dart';
import '../models/ui_settings.dart';
import '../models/sync_metadata.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';

/// UI 설정 데이터 Repository
class UISettingsRepository {
  static const String _tag = 'UISettingsRepository';
  final DatabaseService _databaseService;

  UISettingsRepository(this._databaseService);

  /// UI 설정 조회 (단일 설정만 존재)
  Future<UISettings?> getUISettings() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'ui_settings',
        limit: 1,
      );

      if (maps.isEmpty) {
        LoggerUtils.logDebug('UI 설정이 존재하지 않음 - 기본값 반환', tag: _tag);
        return null;
      }

      final settings = UISettings.fromMap(maps.first);
      LoggerUtils.logDebug('UI 설정 조회 완료', tag: _tag);
      return settings;
    } catch (e) {
      LoggerUtils.logError('UI 설정 조회 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// UI 설정 저장/업데이트
  Future<UISettings> saveUISettings(UISettings settings) async {
    try {
      final db = await _databaseService.database;
      
      // 현재 시간으로 업데이트
      final updatedSettings = settings.copyWith(
        updatedAt: DateTime.now(),
        createdAt: settings.createdAt ?? DateTime.now(),
      );

      // 기존 설정 확인
      final existing = await getUISettings();
      
      if (existing == null) {
        // 새로 삽입
        final id = await db.insert('ui_settings', updatedSettings.toMap());
        final result = updatedSettings.copyWith(id: id);
        LoggerUtils.logInfo('UI 설정 생성 완료: id=$id', tag: _tag);
        return result;
      } else {
        // 업데이트
        final result = updatedSettings.copyWith(id: existing.id);
        await db.update(
          'ui_settings',
          result.toMap(),
          where: 'id = ?',
          whereArgs: [existing.id],
        );
        LoggerUtils.logInfo('UI 설정 업데이트 완료: id=${existing.id}', tag: _tag);
        return result;
      }
    } catch (e) {
      LoggerUtils.logError('UI 설정 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 디바이스별 열 수 업데이트
  Future<UISettings> updateColumnsForDevice(bool isTablet, int columns) async {
    try {
      final current = await getUISettings() ?? const UISettings();
      final updated = current.updateColumnsForDevice(isTablet, columns);
      return await saveUISettings(updated);
    } catch (e) {
      LoggerUtils.logError('디바이스별 열 수 업데이트 실패: isTablet=$isTablet, columns=$columns', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 스마트폰 열 수 업데이트
  Future<UISettings> updateSmartphoneColumns(int columns) async {
    return await updateColumnsForDevice(false, columns);
  }

  /// 타블렛 열 수 업데이트
  Future<UISettings> updateTabletColumns(int columns) async {
    return await updateColumnsForDevice(true, columns);
  }

  /// UI 설정 초기화 (기본값으로 리셋)
  Future<UISettings> resetToDefault() async {
    try {
      const defaultSettings = UISettings();
      return await saveUISettings(defaultSettings);
    } catch (e) {
      LoggerUtils.logError('UI 설정 초기화 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 디바이스별 초기 설정 생성
  Future<UISettings> createDefaultForDevice(bool isTablet) async {
    try {
      final defaultSettings = UISettings(
        portraitColumns: isTablet ? 5 : 4,
        landscapeColumns: isTablet ? 9 : 8,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      return await saveUISettings(defaultSettings);
    } catch (e) {
      LoggerUtils.logError('디바이스별 초기 설정 생성 실패: isTablet=$isTablet', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 동기화 메타데이터 업데이트
  Future<void> updateSyncMetadata(SyncMetadata metadata) async {
    try {
      final current = await getUISettings();
      if (current != null) {
        final updated = current.updateSyncMetadata(metadata);
        await saveUISettings(updated);
        LoggerUtils.logDebug('UI 설정 동기화 메타데이터 업데이트 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('UI 설정 동기화 메타데이터 업데이트 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// UI 설정 삭제
  Future<void> deleteUISettings() async {
    try {
      final db = await _databaseService.database;
      await db.delete('ui_settings');
      LoggerUtils.logInfo('UI 설정 삭제 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('UI 설정 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 디바이스별 열 수 조회
  Future<int> getColumnsForDevice(bool isTablet) async {
    try {
      final settings = await getUISettings();
      return settings?.getColumnsForDevice(isTablet) ?? (isTablet ? 5 : 4);
    } catch (e) {
      LoggerUtils.logError('디바이스별 열 수 조회 실패: isTablet=$isTablet', tag: _tag, error: e);
      // 에러 시 기본값 반환 (새로운 기본값)
      return isTablet ? 5 : 4;
    }
  }

  /// 스마트폰 열 수 조회
  Future<int> getSmartphoneColumns() async {
    return await getColumnsForDevice(false);
  }

  /// 타블렛 열 수 조회
  Future<int> getTabletColumns() async {
    return await getColumnsForDevice(true);
  }

  /// UI 설정 유효성 검사
  Future<bool> validateUISettings(UISettings settings) async {
    try {
      // 기본적으로 스마트폰 기준으로 검사 (더 엄격한 기준)
      return settings.isValid(false);
    } catch (e) {
      LoggerUtils.logError('UI 설정 유효성 검사 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 트랜잭션 내에서 UI 설정 저장 (동기화 방지용)
  Future<void> saveUISettingsInTransaction(Transaction txn, UISettings settings) async {
    try {
      final updatedSettings = settings.copyWith(
        updatedAt: DateTime.now(),
        createdAt: settings.createdAt ?? DateTime.now(),
      );

      // 기존 설정 확인
      final existing = await txn.query('ui_settings', limit: 1);
      
      if (existing.isEmpty) {
        // 새로 삽입
        await txn.insert('ui_settings', updatedSettings.toMap());
        LoggerUtils.logDebug('UI 설정 트랜잭션 내 생성 완료', tag: _tag);
      } else {
        // 업데이트
        final result = updatedSettings.copyWith(id: existing.first['id'] as int);
        await txn.update(
          'ui_settings',
          result.toMap(),
          where: 'id = ?',
          whereArgs: [existing.first['id']],
        );
        LoggerUtils.logDebug('UI 설정 트랜잭션 내 업데이트 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('UI 설정 트랜잭션 내 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }
}
