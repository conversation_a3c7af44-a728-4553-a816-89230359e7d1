import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/payment_method.dart';
import '../utils/logger_utils.dart';

class PaymentMethodsState {
  final List<PaymentMethod> methods;
  final bool isLoading;
  final String? error;

  const PaymentMethodsState({
    this.methods = const [],
    this.isLoading = false,
    this.error,
  });

  PaymentMethodsState copyWith({
    List<PaymentMethod>? methods,
    bool? isLoading,
    String? error,
  }) => PaymentMethodsState(
        methods: methods ?? this.methods,
        isLoading: isLoading ?? this.isLoading,
        error: error,
      );
}

class PaymentMethodsNotifier extends StateNotifier<PaymentMethodsState> {
  static const _tag = 'PaymentMethodsProvider';

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  StreamSubscription? _authSub;
  StreamSubscription<DocumentSnapshot>? _pmDocSub;

  PaymentMethodsNotifier() : super(const PaymentMethodsState()) {
    _start();
  }

  void _start() {
    // 인증 상태를 감지하여 구독을 설정/해제
    _authSub?.cancel();
    _authSub = _auth.authStateChanges().listen((user) async {
      await _subscribeOrInit(user);
    });
    // 현재 사용자에 대해서도 즉시 한번 시도
    _subscribeOrInit(_auth.currentUser);
  }

  Future<void> _subscribeOrInit(User? user) async {
    // 기존 구독 해제
    await _pmDocSub?.cancel();
    _pmDocSub = null;

    if (user == null) {
      LoggerUtils.logDebug('로그인 사용자 없음 - 결제수단 초기화', tag: _tag);
      state = const PaymentMethodsState(methods: []);
      return;
    }

    final docRef = _firestore
        .collection('users')
        .doc(user.uid)
        .collection('settings')
        .doc('payment_methods');

    state = state.copyWith(isLoading: true);

    // 문서가 없으면 기본값으로 생성
    try {
      final snap = await docRef.get();
      if (!snap.exists || (snap.data()?['methods'] == null)) {
        final defaults = _defaultMethods();
        await docRef.set({
          'methods': defaults.map((m) => m.toJson()).toList(),
          'updatedAt': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
        state = state.copyWith(methods: defaults, isLoading: false);
      }
    } catch (e) {
      LoggerUtils.logWarning('결제수단 기본 생성 확인 중 오류: $e', tag: _tag);
    }

    // 실시간 구독 시작
    _pmDocSub = docRef.snapshots().listen((snapshot) {
      if (!snapshot.exists || snapshot.data() == null) {
        return;
      }
      try {
        final data = snapshot.data() as Map<String, dynamic>;
        final list = (data['methods'] as List<dynamic>? ?? [])
            .map((e) => PaymentMethod.fromJson(Map<String, dynamic>.from(e as Map)))
            .toList()
          ..sort((a, b) => a.order.compareTo(b.order));

        // 최소 1개, 최대 3개 보장
        List<PaymentMethod> normalized = list;
        if (normalized.isEmpty) {
          normalized = _defaultMethods();
          _save(normalized); // 서버에 채워넣기
        } else if (normalized.length > 3) {
          normalized = normalized.take(3).toList();
          _save(normalized); // 초과시 상위 3개만 저장
        }

        state = state.copyWith(methods: normalized, isLoading: false, error: null);
      } catch (e) {
        LoggerUtils.logError('결제수단 스냅샷 파싱 실패', tag: _tag, error: e);
        state = state.copyWith(isLoading: false, error: e.toString());
      }
    }, onError: (error) {
      LoggerUtils.logError('결제수단 스냅샷 구독 오류', tag: _tag, error: error);
      state = state.copyWith(isLoading: false, error: error.toString());
    });
  }

  List<PaymentMethod> _defaultMethods() => [
        PaymentMethod(id: 'cash', name: '현금', order: 0),
        PaymentMethod(id: 'transfer', name: '계좌이체', order: 1),
      ];

  Future<void> addMethod(String name) async {
    final trimmed = name.trim();
    if (trimmed.isEmpty) return;
    if (state.methods.length >= 3) return;
    if (state.methods.any((m) => m.name == trimmed)) return;

    final newList = [
      ...state.methods,
      PaymentMethod(id: trimmed.toLowerCase(), name: trimmed, order: state.methods.length),
    ];
    await _save(newList);
  }

  Future<void> removeMethod(String id) async {
    if (state.methods.length <= 1) return; // 최소 1개 유지
    final newList = state.methods.where((m) => m.id != id).toList();
    if (newList.isEmpty) return; // 방어
    for (int i = 0; i < newList.length; i++) {
      newList[i] = newList[i].copyWith(order: i);
    }
    await _save(newList);
  }

  Future<void> reorder(List<PaymentMethod> ordered) async {
    final newList = [
      for (int i = 0; i < ordered.length && i < 3; i++)
        ordered[i].copyWith(order: i)
    ];
    if (newList.isEmpty) return;
    await _save(newList);
  }

  Future<void> _save(List<PaymentMethod> list) async {
    final user = _auth.currentUser;
    if (user == null) return;
    final docRef = _firestore
        .collection('users')
        .doc(user.uid)
        .collection('settings')
        .doc('payment_methods');
    try {
      await docRef.set({
        'methods': list.map((m) => m.toJson()).toList(),
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
    } catch (e) {
      LoggerUtils.logError('결제수단 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  @override
  void dispose() {
    _pmDocSub?.cancel();
    _authSub?.cancel();
    super.dispose();
  }
}

final paymentMethodsProvider = StateNotifierProvider<PaymentMethodsNotifier, PaymentMethodsState>((ref) {
  final notifier = PaymentMethodsNotifier();
  ref.onDispose(() => notifier.dispose());
  return notifier;
});

