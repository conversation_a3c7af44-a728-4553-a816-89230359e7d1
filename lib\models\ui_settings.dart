/// Blue Booth Manager - UI 설정 데이터 모델
///
/// 디바이스별 UI 설정 정보를 표현하는 데이터 모델 클래스입니다.
/// - 스마트폰과 타블렛 별도 열 수 설정
/// - 글로벌 설정으로 모든 행사에서 공유
/// - 실시간 동기화 지원
///
/// 주요 특징:
/// - Freezed 기반 상태 비교 최적화
/// - 불변 객체 패턴 (immutable)
/// - JSON/SQLite 직렬화 지원
/// - copyWith 메서드로 부분 업데이트
/// - 실시간 동기화 메타데이터 포함
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'dart:convert';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'sync_metadata.dart';

part 'ui_settings.freezed.dart';
part 'ui_settings.g.dart';

/// UI 설정 정보를 담는 모델 클래스입니다.
/// freezed를 사용하여 불변 객체로 생성
@freezed
abstract class UISettings with _$UISettings {
  const factory UISettings({
    int? id,
    
    // 세로모드 열 수 (스마트폰 기준 기본값)
    @Default(4) int portraitColumns,

    // 가로모드 열 수 (스마트폰 기준 기본값)
    @Default(8) int landscapeColumns,
    
    // 기타 UI 설정 (향후 확장용)
    @Default(true) bool showProductImages,
    @Default(true) bool showProductDescriptions,
    @Default(false) bool compactMode,
    
    DateTime? createdAt,
    DateTime? updatedAt,

    // 실시간 동기화 메타데이터
    SyncMetadata? syncMetadata,
  }) = _UISettings;

  factory UISettings.fromJson(Map<String, dynamic> json) => _$UISettingsFromJson(json);

  // SQLite 맵에서 직접 생성
  factory UISettings.fromMap(Map<String, dynamic> map) {
    // 동기화 메타데이터 파싱
    SyncMetadata? syncMetadata;
    if (map['syncMetadata'] != null) {
      try {
        final syncMetadataJson = jsonDecode(map['syncMetadata'] as String);
        syncMetadata = SyncMetadata.fromJson(syncMetadataJson);
      } catch (e) {
        // 파싱 실패 시 null로 처리
        syncMetadata = null;
      }
    }

    return UISettings(
      id: map['id'] as int?,
      portraitColumns: map['portraitColumns'] as int? ?? 2,
      landscapeColumns: map['landscapeColumns'] as int? ?? 4,
      showProductImages: (map['showProductImages'] as int? ?? 1) == 1,
      showProductDescriptions: (map['showProductDescriptions'] as int? ?? 1) == 1,
      compactMode: (map['compactMode'] as int? ?? 0) == 1,
      createdAt: map['createdAt'] != null ? DateTime.parse(map['createdAt'] as String) : null,
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : null,
      syncMetadata: syncMetadata,
    );
  }
}

/// UISettings 확장 메서드
extension UISettingsExtension on UISettings {
  /// SQLite 저장용 맵으로 변환
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'portraitColumns': portraitColumns,
      'landscapeColumns': landscapeColumns,
      'showProductImages': showProductImages ? 1 : 0,
      'showProductDescriptions': showProductDescriptions ? 1 : 0,
      'compactMode': compactMode ? 1 : 0,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'syncMetadata': syncMetadata != null ? jsonEncode(syncMetadata!.toJson()) : null,
    };
  }

  /// Firebase 저장용 맵으로 변환 (디바이스별 4개 필드로 확장)
  Map<String, dynamic> toFirebaseMap(bool isTablet) {
    final map = <String, dynamic>{
      'showProductImages': showProductImages,
      'showProductDescriptions': showProductDescriptions,
      'compactMode': compactMode,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };

    // 현재 디바이스 타입에 따라 해당 필드만 업데이트
    if (isTablet) {
      map['tablet_portrait'] = portraitColumns;
      map['tablet_landscape'] = landscapeColumns;
    } else {
      map['smartphone_portrait'] = portraitColumns;
      map['smartphone_landscape'] = landscapeColumns;
    }

    if (syncMetadata != null) {
      map['syncMetadata'] = syncMetadata!.toJson();
    }

    return map;
  }

  /// 디바이스별 열 수 업데이트 (호환성을 위한 메서드)
  UISettings updateColumnsForDevice(bool isTablet, int columns) {
    if (isTablet) {
      // 타블렛의 경우 가로/세로 모두 업데이트 (임시 로직)
      return copyWith(
        portraitColumns: columns,
        landscapeColumns: (columns * 1.5).round().clamp(2, 12),
        updatedAt: DateTime.now(),
      );
    } else {
      // 스마트폰의 경우 가로/세로 모두 업데이트 (임시 로직)
      return copyWith(
        portraitColumns: columns,
        landscapeColumns: (columns + 1).clamp(2, 8),
        updatedAt: DateTime.now(),
      );
    }
  }

  /// 디바이스별 열 수 조회 (호환성을 위한 메서드)
  int getColumnsForDevice(bool isTablet) {
    // 기본적으로 세로모드 열 수 반환
    return portraitColumns;
  }



  /// Firebase에서 현재 디바이스에 맞는 설정 로드
  static UISettings fromFirebaseMap(Map<String, dynamic> data, bool isTablet) {
    final portraitColumns = isTablet
        ? (data['tablet_portrait'] as int? ?? 3)
        : (data['smartphone_portrait'] as int? ?? 2);
    final landscapeColumns = isTablet
        ? (data['tablet_landscape'] as int? ?? 6)
        : (data['smartphone_landscape'] as int? ?? 4);

    return UISettings(
      portraitColumns: portraitColumns,
      landscapeColumns: landscapeColumns,
      showProductImages: data['showProductImages'] as bool? ?? true,
      showProductDescriptions: data['showProductDescriptions'] as bool? ?? true,
      compactMode: data['compactMode'] as bool? ?? false,
      createdAt: data['createdAt'] != null ? DateTime.parse(data['createdAt']) : null,
      updatedAt: data['updatedAt'] != null ? DateTime.parse(data['updatedAt']) : null,
      syncMetadata: data['syncMetadata'] != null
          ? SyncMetadata.fromJson(data['syncMetadata'] as Map<String, dynamic>)
          : null,
    );
  }

  /// 동기화 메타데이터 업데이트
  UISettings updateSyncMetadata(SyncMetadata metadata) {
    return copyWith(syncMetadata: metadata);
  }

  /// 업데이트 시간 갱신
  UISettings updateTimestamp() {
    return copyWith(updatedAt: DateTime.now());
  }

  /// 유효성 검사 (디바이스별)
  bool isValid(bool isTablet) {
    if (isTablet) {
      return portraitColumns >= 4 && portraitColumns <= 7 &&
             landscapeColumns >= 7 && landscapeColumns <= 12;
    } else {
      return portraitColumns >= 2 && portraitColumns <= 5 &&
             landscapeColumns >= 5 && landscapeColumns <= 10;
    }
  }

  /// 기본값 여부 확인 (디바이스별)
  bool isDefault(bool isTablet) {
    if (isTablet) {
      return portraitColumns == 5 && landscapeColumns == 9;
    } else {
      return portraitColumns == 4 && landscapeColumns == 8;
    }
  }

  /// 디바이스별 열 수 조회
  int getColumnsForOrientation(bool isLandscape) {
    return isLandscape ? landscapeColumns : portraitColumns;
  }
}
