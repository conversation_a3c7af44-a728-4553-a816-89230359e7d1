// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ui_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UISettings {

 int? get id;// 세로모드 열 수 (스마트폰 기준 기본값)
 int get portraitColumns;// 가로모드 열 수 (스마트폰 기준 기본값)
 int get landscapeColumns;// 기타 UI 설정 (향후 확장용)
 bool get showProductImages; bool get showProductDescriptions; bool get compactMode; DateTime? get createdAt; DateTime? get updatedAt;// 실시간 동기화 메타데이터
 SyncMetadata? get syncMetadata;
/// Create a copy of UISettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UISettingsCopyWith<UISettings> get copyWith => _$UISettingsCopyWithImpl<UISettings>(this as UISettings, _$identity);

  /// Serializes this UISettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UISettings&&(identical(other.id, id) || other.id == id)&&(identical(other.portraitColumns, portraitColumns) || other.portraitColumns == portraitColumns)&&(identical(other.landscapeColumns, landscapeColumns) || other.landscapeColumns == landscapeColumns)&&(identical(other.showProductImages, showProductImages) || other.showProductImages == showProductImages)&&(identical(other.showProductDescriptions, showProductDescriptions) || other.showProductDescriptions == showProductDescriptions)&&(identical(other.compactMode, compactMode) || other.compactMode == compactMode)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.syncMetadata, syncMetadata) || other.syncMetadata == syncMetadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,portraitColumns,landscapeColumns,showProductImages,showProductDescriptions,compactMode,createdAt,updatedAt,syncMetadata);

@override
String toString() {
  return 'UISettings(id: $id, portraitColumns: $portraitColumns, landscapeColumns: $landscapeColumns, showProductImages: $showProductImages, showProductDescriptions: $showProductDescriptions, compactMode: $compactMode, createdAt: $createdAt, updatedAt: $updatedAt, syncMetadata: $syncMetadata)';
}


}

/// @nodoc
abstract mixin class $UISettingsCopyWith<$Res>  {
  factory $UISettingsCopyWith(UISettings value, $Res Function(UISettings) _then) = _$UISettingsCopyWithImpl;
@useResult
$Res call({
 int? id, int portraitColumns, int landscapeColumns, bool showProductImages, bool showProductDescriptions, bool compactMode, DateTime? createdAt, DateTime? updatedAt, SyncMetadata? syncMetadata
});


$SyncMetadataCopyWith<$Res>? get syncMetadata;

}
/// @nodoc
class _$UISettingsCopyWithImpl<$Res>
    implements $UISettingsCopyWith<$Res> {
  _$UISettingsCopyWithImpl(this._self, this._then);

  final UISettings _self;
  final $Res Function(UISettings) _then;

/// Create a copy of UISettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? portraitColumns = null,Object? landscapeColumns = null,Object? showProductImages = null,Object? showProductDescriptions = null,Object? compactMode = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? syncMetadata = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,portraitColumns: null == portraitColumns ? _self.portraitColumns : portraitColumns // ignore: cast_nullable_to_non_nullable
as int,landscapeColumns: null == landscapeColumns ? _self.landscapeColumns : landscapeColumns // ignore: cast_nullable_to_non_nullable
as int,showProductImages: null == showProductImages ? _self.showProductImages : showProductImages // ignore: cast_nullable_to_non_nullable
as bool,showProductDescriptions: null == showProductDescriptions ? _self.showProductDescriptions : showProductDescriptions // ignore: cast_nullable_to_non_nullable
as bool,compactMode: null == compactMode ? _self.compactMode : compactMode // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,syncMetadata: freezed == syncMetadata ? _self.syncMetadata : syncMetadata // ignore: cast_nullable_to_non_nullable
as SyncMetadata?,
  ));
}
/// Create a copy of UISettings
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SyncMetadataCopyWith<$Res>? get syncMetadata {
    if (_self.syncMetadata == null) {
    return null;
  }

  return $SyncMetadataCopyWith<$Res>(_self.syncMetadata!, (value) {
    return _then(_self.copyWith(syncMetadata: value));
  });
}
}


/// Adds pattern-matching-related methods to [UISettings].
extension UISettingsPatterns on UISettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UISettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UISettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UISettings value)  $default,){
final _that = this;
switch (_that) {
case _UISettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UISettings value)?  $default,){
final _that = this;
switch (_that) {
case _UISettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  int portraitColumns,  int landscapeColumns,  bool showProductImages,  bool showProductDescriptions,  bool compactMode,  DateTime? createdAt,  DateTime? updatedAt,  SyncMetadata? syncMetadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UISettings() when $default != null:
return $default(_that.id,_that.portraitColumns,_that.landscapeColumns,_that.showProductImages,_that.showProductDescriptions,_that.compactMode,_that.createdAt,_that.updatedAt,_that.syncMetadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  int portraitColumns,  int landscapeColumns,  bool showProductImages,  bool showProductDescriptions,  bool compactMode,  DateTime? createdAt,  DateTime? updatedAt,  SyncMetadata? syncMetadata)  $default,) {final _that = this;
switch (_that) {
case _UISettings():
return $default(_that.id,_that.portraitColumns,_that.landscapeColumns,_that.showProductImages,_that.showProductDescriptions,_that.compactMode,_that.createdAt,_that.updatedAt,_that.syncMetadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  int portraitColumns,  int landscapeColumns,  bool showProductImages,  bool showProductDescriptions,  bool compactMode,  DateTime? createdAt,  DateTime? updatedAt,  SyncMetadata? syncMetadata)?  $default,) {final _that = this;
switch (_that) {
case _UISettings() when $default != null:
return $default(_that.id,_that.portraitColumns,_that.landscapeColumns,_that.showProductImages,_that.showProductDescriptions,_that.compactMode,_that.createdAt,_that.updatedAt,_that.syncMetadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UISettings implements UISettings {
  const _UISettings({this.id, this.portraitColumns = 4, this.landscapeColumns = 8, this.showProductImages = true, this.showProductDescriptions = true, this.compactMode = false, this.createdAt, this.updatedAt, this.syncMetadata});
  factory _UISettings.fromJson(Map<String, dynamic> json) => _$UISettingsFromJson(json);

@override final  int? id;
// 세로모드 열 수 (스마트폰 기준 기본값)
@override@JsonKey() final  int portraitColumns;
// 가로모드 열 수 (스마트폰 기준 기본값)
@override@JsonKey() final  int landscapeColumns;
// 기타 UI 설정 (향후 확장용)
@override@JsonKey() final  bool showProductImages;
@override@JsonKey() final  bool showProductDescriptions;
@override@JsonKey() final  bool compactMode;
@override final  DateTime? createdAt;
@override final  DateTime? updatedAt;
// 실시간 동기화 메타데이터
@override final  SyncMetadata? syncMetadata;

/// Create a copy of UISettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UISettingsCopyWith<_UISettings> get copyWith => __$UISettingsCopyWithImpl<_UISettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UISettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UISettings&&(identical(other.id, id) || other.id == id)&&(identical(other.portraitColumns, portraitColumns) || other.portraitColumns == portraitColumns)&&(identical(other.landscapeColumns, landscapeColumns) || other.landscapeColumns == landscapeColumns)&&(identical(other.showProductImages, showProductImages) || other.showProductImages == showProductImages)&&(identical(other.showProductDescriptions, showProductDescriptions) || other.showProductDescriptions == showProductDescriptions)&&(identical(other.compactMode, compactMode) || other.compactMode == compactMode)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.syncMetadata, syncMetadata) || other.syncMetadata == syncMetadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,portraitColumns,landscapeColumns,showProductImages,showProductDescriptions,compactMode,createdAt,updatedAt,syncMetadata);

@override
String toString() {
  return 'UISettings(id: $id, portraitColumns: $portraitColumns, landscapeColumns: $landscapeColumns, showProductImages: $showProductImages, showProductDescriptions: $showProductDescriptions, compactMode: $compactMode, createdAt: $createdAt, updatedAt: $updatedAt, syncMetadata: $syncMetadata)';
}


}

/// @nodoc
abstract mixin class _$UISettingsCopyWith<$Res> implements $UISettingsCopyWith<$Res> {
  factory _$UISettingsCopyWith(_UISettings value, $Res Function(_UISettings) _then) = __$UISettingsCopyWithImpl;
@override @useResult
$Res call({
 int? id, int portraitColumns, int landscapeColumns, bool showProductImages, bool showProductDescriptions, bool compactMode, DateTime? createdAt, DateTime? updatedAt, SyncMetadata? syncMetadata
});


@override $SyncMetadataCopyWith<$Res>? get syncMetadata;

}
/// @nodoc
class __$UISettingsCopyWithImpl<$Res>
    implements _$UISettingsCopyWith<$Res> {
  __$UISettingsCopyWithImpl(this._self, this._then);

  final _UISettings _self;
  final $Res Function(_UISettings) _then;

/// Create a copy of UISettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? portraitColumns = null,Object? landscapeColumns = null,Object? showProductImages = null,Object? showProductDescriptions = null,Object? compactMode = null,Object? createdAt = freezed,Object? updatedAt = freezed,Object? syncMetadata = freezed,}) {
  return _then(_UISettings(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,portraitColumns: null == portraitColumns ? _self.portraitColumns : portraitColumns // ignore: cast_nullable_to_non_nullable
as int,landscapeColumns: null == landscapeColumns ? _self.landscapeColumns : landscapeColumns // ignore: cast_nullable_to_non_nullable
as int,showProductImages: null == showProductImages ? _self.showProductImages : showProductImages // ignore: cast_nullable_to_non_nullable
as bool,showProductDescriptions: null == showProductDescriptions ? _self.showProductDescriptions : showProductDescriptions // ignore: cast_nullable_to_non_nullable
as bool,compactMode: null == compactMode ? _self.compactMode : compactMode // ignore: cast_nullable_to_non_nullable
as bool,createdAt: freezed == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime?,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,syncMetadata: freezed == syncMetadata ? _self.syncMetadata : syncMetadata // ignore: cast_nullable_to_non_nullable
as SyncMetadata?,
  ));
}

/// Create a copy of UISettings
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SyncMetadataCopyWith<$Res>? get syncMetadata {
    if (_self.syncMetadata == null) {
    return null;
  }

  return $SyncMetadataCopyWith<$Res>(_self.syncMetadata!, (value) {
    return _then(_self.copyWith(syncMetadata: value));
  });
}
}

// dart format on
