import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/sales_log_provider.dart';
import '../../providers/prepayment_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/dimens.dart';
import '../../utils/currency_utils.dart';
import '../../models/sales_log.dart';
import '../../models/transaction_type.dart';
import '../../models/prepayment.dart';

/// 실용적인 통계 탭 컨텐츠
///
/// 실제 판매자가 알아야 하는 핵심 비즈니스 데이터에 집중한 통계 화면입니다.
/// - 매출, 할인, 선입금, 거래 유형별 상세 분석
/// - 세트 할인 절약 금액 및 적용 현황
/// - 판매자별 성과 (등수 없이)
/// - 상품별 판매 현황
/// - 일괄 판매 분석
class StatisticsTabContent extends ConsumerStatefulWidget {
  final String selectedSeller;
  final DateTimeRange? selectedDateRange;

  const StatisticsTabContent({
    super.key,
    required this.selectedSeller,
    this.selectedDateRange,
  });

  @override
  ConsumerState<StatisticsTabContent> createState() => _StatisticsTabContentState();
}

class _StatisticsTabContentState extends ConsumerState<StatisticsTabContent> {
  @override
  Widget build(BuildContext context) {
    final salesLogState = ref.watch(salesLogNotifierProvider);
    final prepaymentState = ref.watch(prepaymentNotifierProvider);

    if (salesLogState.isLoading || prepaymentState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (salesLogState.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: Dimens.space16),
            Text(
              '데이터를 불러올 수 없습니다',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.error,
                fontFamily: 'Pretendard',
              ),
            ),
          ],
        ),
      );
    }

    // 필터링된 데이터 계산
    final filteredSalesLogs = _getFilteredSalesLogs(salesLogState.salesLogs);
    final filteredPrepayments = _getFilteredPrepayments(prepaymentState.prepayments);
    final statsData = _calculateStatistics(filteredSalesLogs, filteredPrepayments);

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
        await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(Dimens.space16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 핵심 지표 카드들
            _buildKeyMetricsSection(statsData),
            const SizedBox(height: Dimens.space20),

            // 할인 분석 섹션
            _buildDiscountAnalysisSection(statsData),
            const SizedBox(height: Dimens.space20),

            // 거래 유형별 분석 (실제 금액과 건수)
            _buildTransactionTypeAnalysis(statsData),
            const SizedBox(height: Dimens.space20),

            // 상품 분석 섹션
            _buildProductAnalysisSection(statsData),
            const SizedBox(height: Dimens.space20),

            // 판매자별 성과 (등수 없이)
            if (widget.selectedSeller == '전체 판매자')
              _buildSellerPerformanceSection(statsData),
            if (widget.selectedSeller == '전체 판매자')
              const SizedBox(height: Dimens.space20),

            // 선입금 현황
            _buildPrepaymentStatusSection(statsData),
            const SizedBox(height: Dimens.space20),

            // 일괄 판매 분석
            _buildBatchSalesAnalysisSection(statsData),

            // 하단 여백
            const SizedBox(height: Dimens.space24),
          ],
        ),
      ),
    );
  }

  /// 필터링된 판매 기록 반환
  List<SalesLog> _getFilteredSalesLogs(List<SalesLog> allLogs) {
    return allLogs.where((log) {
      // 판매자 필터
      if (widget.selectedSeller != '전체 판매자' &&
          (log.sellerName ?? '알 수 없음') != widget.selectedSeller) {
        return false;
      }

      // 날짜 범위 필터
      if (widget.selectedDateRange != null) {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
        final startDate = widget.selectedDateRange!.start;
        final endDate = widget.selectedDateRange!.end.add(const Duration(days: 1));

        if (logDate.isBefore(startDate) || logDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// 필터링된 선입금 기록 반환
  List<Prepayment> _getFilteredPrepayments(List<Prepayment> allPrepayments) {
    return allPrepayments.where((prepayment) {
      // 날짜 범위 필터
      if (widget.selectedDateRange != null) {
        final prepaymentDate = prepayment.registrationDate;
        final startDate = widget.selectedDateRange!.start;
        final endDate = widget.selectedDateRange!.end.add(const Duration(days: 1));

        if (prepaymentDate.isBefore(startDate) || prepaymentDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// 통계 데이터 계산
  Map<String, dynamic> _calculateStatistics(List<SalesLog> logs, List<Prepayment> prepayments) {
    if (logs.isEmpty && prepayments.isEmpty) {
      return _getEmptyStats();
    }

    // 기본 매출 통계
    final totalRevenue = logs.fold<int>(0, (sum, log) => sum + log.totalAmount);
    final totalTransactions = logs.length;
    final averageTransaction = totalTransactions > 0 ? totalRevenue ~/ totalTransactions : 0;
    final totalQuantity = logs.fold<int>(0, (sum, log) => sum + log.soldQuantity);

    // 세트 할인 분석
    final totalSetDiscountAmount = logs.fold<int>(0, (sum, log) => sum + log.setDiscountAmount);
    final setDiscountCount = logs.where((log) => log.setDiscountAmount > 0).length;
    final originalTotalBeforeDiscount = logs.fold<int>(0, (sum, log) =>
        sum + (log.soldPrice * log.soldQuantity));

    // 거래 유형별 분석 (건수와 금액)
    final transactionTypeStats = <TransactionType, Map<String, int>>{};
    for (final type in TransactionType.values) {
      final typeLogs = logs.where((log) => log.transactionType == type).toList();
      transactionTypeStats[type] = {
        'count': typeLogs.length,
        'amount': typeLogs.fold<int>(0, (sum, log) => sum + log.totalAmount),
      };
    }

    // 상품별 분석 (수량과 매출)
    final productStats = <String, Map<String, int>>{};
    for (final log in logs) {
      if (!productStats.containsKey(log.productName)) {
        productStats[log.productName] = {'quantity': 0, 'revenue': 0};
      }
      productStats[log.productName]!['quantity'] =
          (productStats[log.productName]!['quantity'] ?? 0) + log.soldQuantity;
      productStats[log.productName]!['revenue'] =
          (productStats[log.productName]!['revenue'] ?? 0) + log.totalAmount;
    }

    // 판매자별 성과
    final sellerStats = <String, Map<String, int>>{};
    for (final log in logs) {
      final seller = log.sellerName ?? '알 수 없음';
      if (!sellerStats.containsKey(seller)) {
        sellerStats[seller] = {'count': 0, 'revenue': 0};
      }
      sellerStats[seller]!['count'] = (sellerStats[seller]!['count'] ?? 0) + 1;
      sellerStats[seller]!['revenue'] = (sellerStats[seller]!['revenue'] ?? 0) + log.totalAmount;
    }

    // 선입금 분석
    final totalPrepaymentAmount = prepayments.fold<int>(0, (sum, p) => sum + p.amount);
    final receivedPrepayments = prepayments.where((p) => p.isReceived).toList();
    final receivedPrepaymentAmount = receivedPrepayments.fold<int>(0, (sum, p) => sum + p.amount);
    final pendingPrepaymentAmount = totalPrepaymentAmount - receivedPrepaymentAmount;

    // 일괄 판매 분석
    final batchSales = logs.where((log) => log.batchSaleId != null).toList();
    final batchSaleIds = batchSales.map((log) => log.batchSaleId).toSet();
    final batchSalesCount = batchSaleIds.length;
    final batchSalesRevenue = batchSales.fold<int>(0, (sum, log) => sum + log.totalAmount);
    final averageBatchSaleAmount = batchSalesCount > 0 ? batchSalesRevenue ~/ batchSalesCount : 0;

    return {
      // 기본 지표
      'totalRevenue': totalRevenue,
      'totalTransactions': totalTransactions,
      'averageTransaction': averageTransaction,
      'totalQuantity': totalQuantity,

      // 할인 분석
      'totalSetDiscountAmount': totalSetDiscountAmount,
      'setDiscountCount': setDiscountCount,
      'originalTotalBeforeDiscount': originalTotalBeforeDiscount,

      // 거래 유형별
      'transactionTypeStats': transactionTypeStats,

      // 상품별
      'productStats': productStats,

      // 판매자별
      'sellerStats': sellerStats,

      // 선입금
      'totalPrepaymentAmount': totalPrepaymentAmount,
      'receivedPrepaymentAmount': receivedPrepaymentAmount,
      'pendingPrepaymentAmount': pendingPrepaymentAmount,
      'totalPrepaymentCount': prepayments.length,
      'receivedPrepaymentCount': receivedPrepayments.length,
      'pendingPrepaymentCount': prepayments.length - receivedPrepayments.length,

      // 일괄 판매
      'batchSalesCount': batchSalesCount,
      'batchSalesRevenue': batchSalesRevenue,
      'averageBatchSaleAmount': averageBatchSaleAmount,
    };
  }

  /// 빈 통계 데이터 반환
  Map<String, dynamic> _getEmptyStats() {
    return {
      'totalRevenue': 0,
      'totalTransactions': 0,
      'averageTransaction': 0,
      'totalQuantity': 0,
      'totalSetDiscountAmount': 0,
      'setDiscountCount': 0,
      'originalTotalBeforeDiscount': 0,
      'transactionTypeStats': <TransactionType, Map<String, int>>{},
      'productStats': <String, Map<String, int>>{},
      'sellerStats': <String, Map<String, int>>{},
      'totalPrepaymentAmount': 0,
      'receivedPrepaymentAmount': 0,
      'pendingPrepaymentAmount': 0,
      'totalPrepaymentCount': 0,
      'receivedPrepaymentCount': 0,
      'pendingPrepaymentCount': 0,
      'batchSalesCount': 0,
      'batchSalesRevenue': 0,
      'averageBatchSaleAmount': 0,
    };
  }

  /// 핵심 지표 섹션
  Widget _buildKeyMetricsSection(Map<String, dynamic> stats) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '핵심 지표',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontFamily: 'Pretendard',
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: Dimens.space12),
        _buildMetricsGrid(stats),
      ],
    );
  }

  /// 할인 분석 섹션
  Widget _buildDiscountAnalysisSection(Map<String, dynamic> stats) {
    return _buildSectionCard(
      title: '할인 분석',
      icon: Icons.discount_outlined,
      child: Column(
        children: [
          _buildStatRow(
            '세트 할인 절약 금액',
            CurrencyUtils.formatCurrency(stats['totalSetDiscountAmount']),
            Icons.savings_outlined,
            AppColors.success,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '세트 할인 적용 건수',
            '${stats['setDiscountCount']}건',
            Icons.local_offer_outlined,
            AppColors.info,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '할인 전 총액',
            CurrencyUtils.formatCurrency(stats['originalTotalBeforeDiscount']),
            Icons.receipt_outlined,
            AppColors.neutral60,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '할인 후 실제 매출',
            CurrencyUtils.formatCurrency(stats['totalRevenue']),
            Icons.account_balance_wallet_outlined,
            AppColors.primarySeed,
          ),
        ],
      ),
    );
  }

  /// 지표 그리드
  Widget _buildMetricsGrid(Map<String, dynamic> stats) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth >= 600;

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: isTablet ? 4 : 2,
      crossAxisSpacing: Dimens.space12,
      mainAxisSpacing: Dimens.space12,
      childAspectRatio: isTablet ? 1.2 : 1.1,
      children: [
        _buildMetricCard(
          title: '총 매출',
          value: CurrencyUtils.formatCurrency(stats['totalRevenue']),
          icon: Icons.account_balance_wallet_rounded,
          color: AppColors.primarySeed,
          gradient: [
            AppColors.primarySeed.withValues(alpha: 0.1),
            AppColors.primarySeed.withValues(alpha: 0.05),
          ],
        ),
        _buildMetricCard(
          title: '총 거래수',
          value: '${stats['totalTransactions']}건',
          icon: Icons.receipt_long_rounded,
          color: AppColors.secondary,
          gradient: [
            AppColors.secondary.withValues(alpha: 0.1),
            AppColors.secondary.withValues(alpha: 0.05),
          ],
        ),
        _buildMetricCard(
          title: '평균 거래액',
          value: CurrencyUtils.formatCurrency(stats['averageTransaction']),
          icon: Icons.trending_up_rounded,
          color: AppColors.accent,
          gradient: [
            AppColors.accent.withValues(alpha: 0.1),
            AppColors.accent.withValues(alpha: 0.05),
          ],
        ),
        _buildMetricCard(
          title: '총 판매량',
          value: '${stats['totalQuantity']}개',
          icon: Icons.inventory_rounded,
          color: AppColors.info,
          gradient: [
            AppColors.info.withValues(alpha: 0.1),
            AppColors.info.withValues(alpha: 0.05),
          ],
        ),
      ],
    );
  }

  /// 지표 카드
  Widget _buildMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required List<Color> gradient,
  }) {
    return Container(
      padding: const EdgeInsets.all(Dimens.space16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradient,
        ),
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.elevation1,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(Dimens.space8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(Dimens.radiusM),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: Dimens.space8),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontFamily: 'Pretendard',
              color: AppColors.neutral60,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: Dimens.space4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontFamily: 'Pretendard',
              fontWeight: FontWeight.bold,
              color: AppColors.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 거래 유형별 분석 섹션
  Widget _buildTransactionTypeAnalysis(Map<String, dynamic> stats) {
    final transactionTypeStats = stats['transactionTypeStats'] as Map<TransactionType, Map<String, int>>;

    return _buildSectionCard(
      title: '거래 유형별 분석',
      icon: Icons.analytics_outlined,
      child: Column(
        children: TransactionType.values.map((type) {
          final typeStats = transactionTypeStats[type] ?? {'count': 0, 'amount': 0};
          final count = typeStats['count'] ?? 0;
          final amount = typeStats['amount'] ?? 0;

          if (count == 0) return const SizedBox.shrink();

          return Padding(
            padding: const EdgeInsets.only(bottom: Dimens.space12),
            child: _buildStatRow(
              type.displayName,
              '${count}건 • ${CurrencyUtils.formatCurrency(amount)}',
              _getTransactionTypeIcon(type),
              _getTransactionTypeColor(type),
            ),
          );
        }).where((widget) => widget is! SizedBox).toList(),
      ),
    );
  }

  /// 상품 분석 섹션
  Widget _buildProductAnalysisSection(Map<String, dynamic> stats) {
    final productStats = stats['productStats'] as Map<String, Map<String, int>>;

    if (productStats.isEmpty) {
      return _buildEmptySection('상품 분석');
    }

    // 매출 기준 정렬
    final sortedByRevenue = productStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));

    // 수량 기준 정렬
    final sortedByQuantity = productStats.entries.toList()
      ..sort((a, b) => (b.value['quantity'] ?? 0).compareTo(a.value['quantity'] ?? 0));

    return _buildSectionCard(
      title: '상품 분석',
      icon: Icons.inventory_2_outlined,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 매출 기준 베스트셀러
          Text(
            '매출 기준 TOP 5',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontFamily: 'Pretendard',
              fontWeight: FontWeight.w600,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: Dimens.space8),
          ...sortedByRevenue.take(5).map((entry) {
            final revenue = entry.value['revenue'] ?? 0;
            final quantity = entry.value['quantity'] ?? 0;
            return Padding(
              padding: const EdgeInsets.only(bottom: Dimens.space8),
              child: _buildStatRow(
                entry.key,
                '${CurrencyUtils.formatCurrency(revenue)} • ${quantity}개',
                Icons.trending_up,
                AppColors.primarySeed,
              ),
            );
          }).toList(),

          const SizedBox(height: Dimens.space16),

          // 수량 기준 베스트셀러
          Text(
            '판매량 기준 TOP 5',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontFamily: 'Pretendard',
              fontWeight: FontWeight.w600,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: Dimens.space8),
          ...sortedByQuantity.take(5).map((entry) {
            final revenue = entry.value['revenue'] ?? 0;
            final quantity = entry.value['quantity'] ?? 0;
            return Padding(
              padding: const EdgeInsets.only(bottom: Dimens.space8),
              child: _buildStatRow(
                entry.key,
                '${quantity}개 • ${CurrencyUtils.formatCurrency(revenue)}',
                Icons.inventory,
                AppColors.secondary,
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  /// 판매자별 성과 섹션 (등수 없이)
  Widget _buildSellerPerformanceSection(Map<String, dynamic> stats) {
    final sellerStats = stats['sellerStats'] as Map<String, Map<String, int>>;

    if (sellerStats.isEmpty) {
      return _buildEmptySection('판매자별 성과');
    }

    final sortedSellers = sellerStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));

    return _buildSectionCard(
      title: '판매자별 성과',
      icon: Icons.people_outlined,
      child: Column(
        children: sortedSellers.map((entry) {
          final revenue = entry.value['revenue'] ?? 0;
          final count = entry.value['count'] ?? 0;
          final averagePerTransaction = count > 0 ? revenue ~/ count : 0;

          return Padding(
            padding: const EdgeInsets.only(bottom: Dimens.space12),
            child: Container(
              padding: const EdgeInsets.all(Dimens.space12),
              decoration: BoxDecoration(
                color: AppColors.neutral10,
                borderRadius: BorderRadius.circular(Dimens.radiusM),
                border: Border.all(
                  color: AppColors.neutral30,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(Dimens.space8),
                        decoration: BoxDecoration(
                          color: AppColors.primarySeed.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(Dimens.radiusS),
                        ),
                        child: Icon(
                          Icons.person,
                          size: 16,
                          color: AppColors.primarySeed,
                        ),
                      ),
                      const SizedBox(width: Dimens.space8),
                      Expanded(
                        child: Text(
                          entry.key,
                          style: const TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.onSurface,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: Dimens.space8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '총 매출: ${CurrencyUtils.formatCurrency(revenue)}',
                        style: const TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: 14,
                          color: AppColors.onSurface,
                        ),
                      ),
                      Text(
                        '${count}건',
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: 14,
                          color: AppColors.neutral60,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: Dimens.space4),
                  Text(
                    '평균 거래액: ${CurrencyUtils.formatCurrency(averagePerTransaction)}',
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 12,
                      color: AppColors.neutral60,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 선입금 현황 섹션
  Widget _buildPrepaymentStatusSection(Map<String, dynamic> stats) {
    return _buildSectionCard(
      title: '선입금 현황',
      icon: Icons.account_balance_outlined,
      child: Column(
        children: [
          _buildStatRow(
            '총 선입금 금액',
            CurrencyUtils.formatCurrency(stats['totalPrepaymentAmount']),
            Icons.savings_outlined,
            AppColors.info,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '수령 완료',
            '${CurrencyUtils.formatCurrency(stats['receivedPrepaymentAmount'])} (${stats['receivedPrepaymentCount']}건)',
            Icons.check_circle_outline,
            AppColors.success,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '미수령',
            '${CurrencyUtils.formatCurrency(stats['pendingPrepaymentAmount'])} (${stats['pendingPrepaymentCount']}건)',
            Icons.pending_outlined,
            AppColors.warning,
          ),
        ],
      ),
    );
  }

  /// 일괄 판매 분석 섹션
  Widget _buildBatchSalesAnalysisSection(Map<String, dynamic> stats) {
    return _buildSectionCard(
      title: '일괄 판매 분석',
      icon: Icons.shopping_cart_outlined,
      child: Column(
        children: [
          _buildStatRow(
            '일괄 판매 건수',
            '${stats['batchSalesCount']}건',
            Icons.shopping_basket_outlined,
            AppColors.secondary,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '일괄 판매 총액',
            CurrencyUtils.formatCurrency(stats['batchSalesRevenue']),
            Icons.receipt_long_outlined,
            AppColors.primarySeed,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '평균 일괄 판매액',
            CurrencyUtils.formatCurrency(stats['averageBatchSaleAmount']),
            Icons.calculate_outlined,
            AppColors.accent,
          ),
        ],
      ),
    );
  }

  /// 공통 섹션 카드
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(Dimens.space20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        border: Border.all(
          color: AppColors.neutral60.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.elevation1,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(Dimens.space8),
                decoration: BoxDecoration(
                  color: AppColors.primarySeed.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(Dimens.radiusS),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primarySeed,
                  size: 20,
                ),
              ),
              const SizedBox(width: Dimens.space12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontFamily: 'Pretendard',
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimens.space16),
          child,
        ],
      ),
    );
  }

  /// 빈 섹션 위젯
  Widget _buildEmptySection(String title) {
    return Container(
      width: double.infinity,
      height: 120,
      padding: const EdgeInsets.all(Dimens.space20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        border: Border.all(
          color: AppColors.neutral60.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: 32,
            color: AppColors.neutral40,
          ),
          const SizedBox(height: Dimens.space8),
          Text(
            '$title 데이터가 없습니다',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontFamily: 'Pretendard',
              color: AppColors.neutral60,
            ),
          ),
        ],
      ),
    );
  }

  /// 통계 행 위젯
  Widget _buildStatRow(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(Dimens.space6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(Dimens.radiusS),
          ),
          child: Icon(
            icon,
            size: 16,
            color: color,
          ),
        ),
        const SizedBox(width: Dimens.space12),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.onSurface,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontFamily: 'Pretendard',
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// 거래 유형별 색상 반환
  Color _getTransactionTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return AppColors.primarySeed;
      case TransactionType.service:
        return AppColors.secondary;
      case TransactionType.discount:
        return AppColors.error;

      case TransactionType.setDiscount:
        return AppColors.warning;
    }
  }

  /// 거래 유형별 아이콘 반환
  IconData _getTransactionTypeIcon(TransactionType type) {
    switch (type) {
      case TransactionType.sale:
        return Icons.shopping_cart_outlined;
      case TransactionType.service:
        return Icons.build_outlined;
      case TransactionType.discount:
        return Icons.discount_outlined;

      case TransactionType.setDiscount:
        return Icons.local_offer_outlined;
    }
  }
}
