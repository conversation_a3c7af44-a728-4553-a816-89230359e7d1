import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../providers/sales_log_provider.dart';
import '../../providers/prepayment_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/dimens.dart';
import '../../utils/currency_utils.dart';
import '../../models/sales_log.dart';
import '../../models/transaction_type.dart';
import '../../models/prepayment.dart';
import '../../utils/dialog_theme.dart' as custom_dialog;
import 'package:intl/intl.dart';
/// 차트 데이터 모델
class ChartData {
  final String category;
  final double value;
  final Color color;

  ChartData(this.category, this.value, this.color);
}

/// 실용적인 통계 탭 컨텐츠
///
/// 실제 판매자가 알아야 하는 핵심 비즈니스 데이터에 집중한 통계 화면입니다.
/// - 매출, 할인, 선입금, 거래 유형별 상세 분석
/// - 세트 할인 절약 금액 및 적용 현황
/// - 판매자별 성과 (등수 없이)
/// - 상품별 판매 현황
/// - 일괄 판매 분석
class StatisticsTabContent extends ConsumerStatefulWidget {
  final String selectedSeller;
  final DateTimeRange? selectedDateRange;

  const StatisticsTabContent({
    super.key,
    required this.selectedSeller,
    this.selectedDateRange,
  });

  @override
  ConsumerState<StatisticsTabContent> createState() => _StatisticsTabContentState();
}

class _StatisticsTabContentState extends ConsumerState<StatisticsTabContent> {
  @override
  Widget build(BuildContext context) {
    final salesLogState = ref.watch(salesLogNotifierProvider);
    final prepaymentState = ref.watch(prepaymentNotifierProvider);

    if (salesLogState.isLoading || prepaymentState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (salesLogState.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: Dimens.space16),
            Text(
              '데이터를 불러올 수 없습니다',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.error,
                fontFamily: 'Pretendard',
              ),
            ),
          ],
        ),
      );
    }

    // 필터링된 데이터 계산
    final filteredSalesLogs = _getFilteredSalesLogs(salesLogState.salesLogs);
    final filteredPrepayments = _getFilteredPrepayments(prepaymentState.prepayments);
    final statsData = _calculateStatistics(filteredSalesLogs, filteredPrepayments);

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
        await ref.read(prepaymentNotifierProvider.notifier).loadPrepayments();
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(Dimens.space16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 모던 대시보드 레이아웃
            _buildModernDashboard(
              context: context,
              stats: statsData,
              logs: filteredSalesLogs,
              prepayments: filteredPrepayments,
            ),
            const SizedBox(height: Dimens.space24),
          ],
        ),
      ),
    );
  }

  /// 필터링된 판매 기록 반환
  List<SalesLog> _getFilteredSalesLogs(List<SalesLog> allLogs) {
    return allLogs.where((log) {
      // 판매자 필터
      if (widget.selectedSeller != '전체 판매자' &&
          (log.sellerName ?? '알 수 없음') != widget.selectedSeller) {
        return false;
      }

      // 날짜 범위 필터
      if (widget.selectedDateRange != null) {
        final logDate = DateTime.fromMillisecondsSinceEpoch(log.saleTimestamp);
        final startDate = widget.selectedDateRange!.start;
        final endDate = widget.selectedDateRange!.end.add(const Duration(days: 1));

        if (logDate.isBefore(startDate) || logDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// 필터링된 선입금 기록 반환
  List<Prepayment> _getFilteredPrepayments(List<Prepayment> allPrepayments) {
    return allPrepayments.where((prepayment) {
      // 날짜 범위 필터
      if (widget.selectedDateRange != null) {
        final prepaymentDate = prepayment.registrationDate;
        final startDate = widget.selectedDateRange!.start;
        final endDate = widget.selectedDateRange!.end.add(const Duration(days: 1));

        if (prepaymentDate.isBefore(startDate) || prepaymentDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// 통계 데이터 계산
  Map<String, dynamic> _calculateStatistics(List<SalesLog> logs, List<Prepayment> prepayments) {
    if (logs.isEmpty && prepayments.isEmpty) {
      return _getEmptyStats();
    }

    // 기본 매출 통계
    final totalRevenue = logs.fold<int>(0, (sum, log) => sum + log.totalAmount);
    final totalTransactions = logs.length;
    final averageTransaction = totalTransactions > 0 ? totalRevenue ~/ totalTransactions : 0;
    final totalQuantity = logs.fold<int>(0, (sum, log) => sum + log.soldQuantity);

    // 세트 할인 분석
    final totalSetDiscountAmount = logs.fold<int>(0, (sum, log) => sum + log.setDiscountAmount);
    final setDiscountCount = logs.where((log) => log.setDiscountAmount > 0).length;
    final originalTotalBeforeDiscount = logs.fold<int>(0, (sum, log) =>
        sum + (log.soldPrice * log.soldQuantity));

    // 거래 유형별 분석 (건수와 금액)
    final transactionTypeStats = <TransactionType, Map<String, int>>{};
    for (final type in TransactionType.values) {
      final typeLogs = logs.where((log) => log.transactionType == type).toList();
      transactionTypeStats[type] = {
        'count': typeLogs.length,
        'amount': typeLogs.fold<int>(0, (sum, log) => sum + log.totalAmount),
      };
    }

    // 상품별 분석 (수량과 매출)
    final productStats = <String, Map<String, int>>{};
    for (final log in logs) {
      if (!productStats.containsKey(log.productName)) {
        productStats[log.productName] = {'quantity': 0, 'revenue': 0};
      }
      productStats[log.productName]!['quantity'] =
          (productStats[log.productName]!['quantity'] ?? 0) + log.soldQuantity;
      productStats[log.productName]!['revenue'] =
          (productStats[log.productName]!['revenue'] ?? 0) + log.totalAmount;
    }

    // 판매자별 성과
    final sellerStats = <String, Map<String, int>>{};
    for (final log in logs) {
      final seller = log.sellerName ?? '알 수 없음';
      if (!sellerStats.containsKey(seller)) {
        sellerStats[seller] = {'count': 0, 'revenue': 0};
      }
      sellerStats[seller]!['count'] = (sellerStats[seller]!['count'] ?? 0) + 1;
      sellerStats[seller]!['revenue'] = (sellerStats[seller]!['revenue'] ?? 0) + log.totalAmount;
    }

    // 선입금 분석
    final totalPrepaymentAmount = prepayments.fold<int>(0, (sum, p) => sum + p.amount);
    final receivedPrepayments = prepayments.where((p) => p.isReceived).toList();
    final receivedPrepaymentAmount = receivedPrepayments.fold<int>(0, (sum, p) => sum + p.amount);
    final pendingPrepaymentAmount = totalPrepaymentAmount - receivedPrepaymentAmount;

    // 서비스 제공 분석 (판매가격이 0인 경우를 서비스로 간주)
    final serviceLogs = logs.where((log) => log.soldPrice == 0).toList();
    final serviceCount = serviceLogs.length;
    final serviceQuantity = serviceLogs.fold<int>(0, (sum, log) => sum + log.soldQuantity);
    final uniqueServiceProducts = serviceLogs.map((log) => log.productName).toSet().length;

    return {
      // 기본 지표
      'totalRevenue': totalRevenue,
      'totalTransactions': totalTransactions,
      'averageTransaction': averageTransaction,
      'totalQuantity': totalQuantity,

      // 할인 분석
      'totalSetDiscountAmount': totalSetDiscountAmount,
      'setDiscountCount': setDiscountCount,
      'originalTotalBeforeDiscount': originalTotalBeforeDiscount,

      // 거래 유형별
      'transactionTypeStats': transactionTypeStats,

      // 상품별
      'productStats': productStats,

      // 판매자별
      'sellerStats': sellerStats,

      // 선입금
      'totalPrepaymentAmount': totalPrepaymentAmount,
      'receivedPrepaymentAmount': receivedPrepaymentAmount,
      'pendingPrepaymentAmount': pendingPrepaymentAmount,
      'totalPrepaymentCount': prepayments.length,
      'receivedPrepaymentCount': receivedPrepayments.length,
      'pendingPrepaymentCount': prepayments.length - receivedPrepayments.length,

      // 서비스 제공
      'serviceCount': serviceCount,
      'serviceQuantity': serviceQuantity,
      'uniqueServiceProducts': uniqueServiceProducts,
    };
  }

  /// 빈 통계 데이터 반환
  Map<String, dynamic> _getEmptyStats() {
    return {
      'totalRevenue': 0,
      'totalTransactions': 0,
      'averageTransaction': 0,
      'totalQuantity': 0,
      'totalSetDiscountAmount': 0,
      'setDiscountCount': 0,
      'originalTotalBeforeDiscount': 0,
      'transactionTypeStats': <TransactionType, Map<String, int>>{},
      'productStats': <String, Map<String, int>>{},
      'sellerStats': <String, Map<String, int>>{},
      'totalPrepaymentAmount': 0,
      'receivedPrepaymentAmount': 0,
      'pendingPrepaymentAmount': 0,
      'totalPrepaymentCount': 0,
      'receivedPrepaymentCount': 0,
      'pendingPrepaymentCount': 0,
      'serviceCount': 0,
      'serviceQuantity': 0,
      'uniqueServiceProducts': 0,
    };
  }



  /// 할인 분석 섹션
  Widget _buildDiscountAnalysisSection(Map<String, dynamic> stats) {
    return _buildSectionCard(
      title: '할인 분석',
      icon: Icons.discount_outlined,
      child: Column(
        children: [
          _buildStatRow(
            '세트 할인 절약 금액',
            CurrencyUtils.formatCurrency(stats['totalSetDiscountAmount']),
            Icons.savings_outlined,
            AppColors.success,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '세트 할인 적용 건수',
            '${stats['setDiscountCount']}건',
            Icons.local_offer_outlined,
            AppColors.info,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '할인 전 총액',
            CurrencyUtils.formatCurrency(stats['originalTotalBeforeDiscount']),
            Icons.receipt_outlined,
            AppColors.neutral60,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '할인 후 실제 매출',
            CurrencyUtils.formatCurrency(stats['totalRevenue']),
            Icons.account_balance_wallet_outlined,
            AppColors.primarySeed,
          ),
        ],
      ),
    );
  }

  /// 모던 대시보드 레이아웃
  Widget _buildModernDashboard({
    required BuildContext context,
    required Map<String, dynamic> stats,
    required List<SalesLog> logs,
    required List<Prepayment> prepayments,
  }) {
    final width = MediaQuery.of(context).size.width;
    final isTablet = width >= 600;
    final isWide = width >= 1000;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Hero Section - 총 매출 강조
        _buildHeroSection(stats, isTablet, isWide),
        const SizedBox(height: Dimens.space20),

        // Analytics Section - 차트와 시각화
        _buildAnalyticsSection(stats, isTablet, isWide),
        const SizedBox(height: Dimens.space20),

        // Secondary Metrics - 보조 지표들
        _buildSecondaryMetrics(stats, isTablet, isWide),
      ],
    );
  }

  /// Hero Section - 핵심 지표 통합
  Widget _buildHeroSection(Map<String, dynamic> stats, bool isTablet, bool isWide) {
    // 가독성 중심의 색상 정의
    const primaryColor = Color(0xFF2563EB); // 파란색
    const secondaryColor = Color(0xFF059669); // 초록색
    const accentColor = Color(0xFF7C3AED); // 보라색
    const infoColor = Color(0xFF0891B2); // 청록색

    if (isWide) {
      // 와이드 스크린: 4개 지표를 가로로 배치
      return Row(
        children: [
          Expanded(
            child: _buildIntegratedMetricCard(
              title: '총 매출',
              value: CurrencyUtils.formatCurrency(stats['totalRevenue']),
              icon: Icons.account_balance_wallet_rounded,
              color: primaryColor,
              onTap: () => _showSalesOverviewDialog(stats),
            ),
          ),
          const SizedBox(width: Dimens.space12),
          Expanded(
            child: _buildIntegratedMetricCard(
              title: '총 거래수',
              value: '${stats['totalTransactions']}건',
              icon: Icons.receipt_long_rounded,
              color: secondaryColor,
              onTap: () => _showSalesOverviewDialog(stats),
            ),
          ),
          const SizedBox(width: Dimens.space12),
          Expanded(
            child: _buildIntegratedMetricCard(
              title: '평균 거래액',
              value: CurrencyUtils.formatCurrency(stats['averageTransaction']),
              icon: Icons.trending_up_rounded,
              color: accentColor,
              onTap: () => _showSalesOverviewDialog(stats),
            ),
          ),
          const SizedBox(width: Dimens.space12),
          Expanded(
            child: _buildIntegratedMetricCard(
              title: '총 판매량',
              value: '${stats['totalQuantity']}개',
              icon: Icons.inventory_rounded,
              color: infoColor,
              onTap: () => _showSalesOverviewDialog(stats),
            ),
          ),
        ],
      );
    } else if (isTablet) {
      // 태블릿: 2x2 그리드
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildIntegratedMetricCard(
                  title: '총 매출',
                  value: CurrencyUtils.formatCurrency(stats['totalRevenue']),
                  icon: Icons.account_balance_wallet_rounded,
                  color: primaryColor,
                  onTap: () => _showSalesOverviewDialog(stats),
                ),
              ),
              const SizedBox(width: Dimens.space12),
              Expanded(
                child: _buildIntegratedMetricCard(
                  title: '총 거래수',
                  value: '${stats['totalTransactions']}건',
                  icon: Icons.receipt_long_rounded,
                  color: secondaryColor,
                  onTap: () => _showSalesOverviewDialog(stats),
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimens.space12),
          Row(
            children: [
              Expanded(
                child: _buildIntegratedMetricCard(
                  title: '평균 거래액',
                  value: CurrencyUtils.formatCurrency(stats['averageTransaction']),
                  icon: Icons.trending_up_rounded,
                  color: accentColor,
                  onTap: () => _showSalesOverviewDialog(stats),
                ),
              ),
              const SizedBox(width: Dimens.space12),
              Expanded(
                child: _buildIntegratedMetricCard(
                  title: '총 판매량',
                  value: '${stats['totalQuantity']}개',
                  icon: Icons.inventory_rounded,
                  color: infoColor,
                  onTap: () => _showSalesOverviewDialog(stats),
                ),
              ),
            ],
          ),
        ],
      );
    } else {
      // 모바일: 세로 배치
      return Column(
        children: [
          _buildIntegratedMetricCard(
            title: '총 매출',
            value: CurrencyUtils.formatCurrency(stats['totalRevenue']),
            icon: Icons.account_balance_wallet_rounded,
            color: primaryColor,
            onTap: () => _showSalesOverviewDialog(stats),
          ),
          const SizedBox(height: Dimens.space12),
          Row(
            children: [
              Expanded(
                child: _buildIntegratedMetricCard(
                  title: '총 거래수',
                  value: '${stats['totalTransactions']}건',
                  icon: Icons.receipt_long_rounded,
                  color: secondaryColor,
                  onTap: () => _showSalesOverviewDialog(stats),
                ),
              ),
              const SizedBox(width: Dimens.space12),
              Expanded(
                child: _buildIntegratedMetricCard(
                  title: '평균 거래액',
                  value: CurrencyUtils.formatCurrency(stats['averageTransaction']),
                  icon: Icons.trending_up_rounded,
                  color: accentColor,
                  onTap: () => _showSalesOverviewDialog(stats),
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimens.space12),
          _buildIntegratedMetricCard(
            title: '총 판매량',
            value: '${stats['totalQuantity']}개',
            icon: Icons.inventory_rounded,
            color: infoColor,
            onTap: () => _showSalesOverviewDialog(stats),
          ),
        ],
      );
    }
  }
  /// Hero Card - 메인 지표용 큰 카드
  Widget _buildHeroCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required List<Color> gradient,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(Dimens.radiusXL),
      child: Container(
        padding: const EdgeInsets.all(Dimens.space24),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              gradient[0],
              gradient[1],
            ],
          ),
          borderRadius: BorderRadius.circular(Dimens.radiusXL),
          boxShadow: [
            BoxShadow(
              color: gradient[0].withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(Dimens.space12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(Dimens.radiusL),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white.withValues(alpha: 0.7),
                  size: 16,
                ),
              ],
            ),
            const SizedBox(height: Dimens.space20),
            Text(
              title,
              style: const TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: Dimens.space8),
            Text(
              value,
              style: const TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: Dimens.space4),
            Text(
              subtitle,
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 14,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Compact Metric Card - 작은 지표 카드
  Widget _buildCompactMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(Dimens.space16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.elevation1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(Dimens.space8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(Dimens.radiusM),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(height: Dimens.space12),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: Dimens.space4),
          Text(
            title,
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 12,
              color: AppColors.neutral60,
            ),
          ),
        ],
      ),
    );
  }

  /// 통합 메트릭 카드 - 가독성 중심 디자인
  Widget _buildIntegratedMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(Dimens.radiusL),
      child: Container(
        padding: const EdgeInsets.all(Dimens.space20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(Dimens.radiusL),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(Dimens.space8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(Dimens.radiusS),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: Colors.grey.shade400,
                  size: 14,
                ),
              ],
            ),
            const SizedBox(height: Dimens.space16),
            Text(
              title,
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: Dimens.space4),
            Text(
              value,
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Analytics Section - 차트와 분석
  Widget _buildAnalyticsSection(Map<String, dynamic> stats, bool isTablet, bool isWide) {
    if (isWide) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: _buildTransactionTypeChart(stats),
          ),
          const SizedBox(width: Dimens.space16),
          Expanded(
            flex: 3,
            child: _buildProductChart(stats),
          ),
        ],
      );
    } else {
      return Column(
        children: [
          _buildTransactionTypeChart(stats),
          const SizedBox(height: Dimens.space16),
          _buildProductChart(stats),
        ],
      );
    }
  }
  /// 거래 유형별 도넛 차트
  Widget _buildTransactionTypeChart(Map<String, dynamic> stats) {
    final transactionTypeStats = stats['transactionTypeStats'] as Map<TransactionType, Map<String, int>>;

    // 가독성 중심 색상 팔레트
    final List<Color> colors = [
      const Color(0xFF2563EB), // 파란색
      const Color(0xFF059669), // 초록색
      const Color(0xFF7C3AED), // 보라색
      const Color(0xFFDC2626), // 빨간색
    ];

    // 차트 데이터 준비
    final List<ChartData> chartData = [];

    int colorIndex = 0;
    for (final entry in transactionTypeStats.entries) {
      final amount = entry.value['amount'] ?? 0;
      if (amount > 0) {
        chartData.add(ChartData(
          entry.key.displayName,
          amount.toDouble(),
          colors[colorIndex % colors.length],
        ));
        colorIndex++;
      }
    }

    return Container(
      padding: const EdgeInsets.all(Dimens.space20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(Dimens.space8),
                decoration: BoxDecoration(
                  color: const Color(0xFF2563EB).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(Dimens.radiusS),
                ),
                child: const Icon(
                  Icons.pie_chart_rounded,
                  color: Color(0xFF2563EB),
                  size: 20,
                ),
              ),
              const SizedBox(width: Dimens.space12),
              Text(
                '카테고리별 판매',
                style: TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimens.space20),
          if (chartData.isNotEmpty)
            SizedBox(
              height: 200,
              child: SfCircularChart(
                legend: Legend(
                  isVisible: true,
                  position: LegendPosition.right,
                  textStyle: const TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: 12,
                  ),
                ),
                series: <CircularSeries>[
                  DoughnutSeries<ChartData, String>(
                    dataSource: chartData,
                    xValueMapper: (ChartData data, _) => data.category,
                    yValueMapper: (ChartData data, _) => data.value,
                    pointColorMapper: (ChartData data, _) => data.color,
                    innerRadius: '60%',
                    dataLabelSettings: const DataLabelSettings(
                      isVisible: true,
                      labelPosition: ChartDataLabelPosition.outside,
                      textStyle: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            )
          else
            const SizedBox(
              height: 200,
              child: Center(
                child: Text(
                  '데이터가 없습니다',
                  style: TextStyle(
                    fontFamily: 'Pretendard',
                    color: AppColors.neutral60,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 상품별 수평 바 차트
  Widget _buildProductChart(Map<String, dynamic> stats) {
    final productStats = stats['productStats'] as Map<String, Map<String, int>>;

    // TOP 5 상품 데이터 준비
    final sortedProducts = productStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));

    final List<ChartData> chartData = [];
    final topProducts = sortedProducts.take(5).toList();

    // 가독성 중심 색상 (그라데이션)
    const baseColor = Color(0xFF059669); // 초록색
    for (int i = 0; i < topProducts.length; i++) {
      final entry = topProducts[i];
      final revenue = entry.value['revenue'] ?? 0;
      chartData.add(ChartData(
        entry.key.length > 10 ? '${entry.key.substring(0, 10)}...' : entry.key,
        revenue.toDouble(),
        baseColor.withValues(alpha: 1.0 - (i * 0.15)),
      ));
    }

    return Container(
      padding: const EdgeInsets.all(Dimens.space20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(Dimens.space8),
                decoration: BoxDecoration(
                  color: const Color(0xFF059669).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(Dimens.radiusS),
                ),
                child: const Icon(
                  Icons.bar_chart_rounded,
                  color: Color(0xFF059669),
                  size: 20,
                ),
              ),
              const SizedBox(width: Dimens.space12),
              Text(
                '상품별 매출 TOP 5',
                style: TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimens.space20),
          if (chartData.isNotEmpty)
            SizedBox(
              height: 250,
              child: SfCartesianChart(
                primaryXAxis: CategoryAxis(
                  labelStyle: TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: 10,
                    color: Colors.grey.shade600,
                  ),
                ),
                primaryYAxis: NumericAxis(
                  numberFormat: NumberFormat.compact(),
                  labelStyle: TextStyle(
                    fontFamily: 'Pretendard',
                    fontSize: 10,
                    color: Colors.grey.shade600,
                  ),
                ),
                plotAreaBorderWidth: 0,
                series: <CartesianSeries>[
                  BarSeries<ChartData, String>(
                    dataSource: chartData,
                    xValueMapper: (ChartData data, _) => data.category,
                    yValueMapper: (ChartData data, _) => data.value,
                    pointColorMapper: (ChartData data, _) => data.color,
                    borderRadius: const BorderRadius.all(Radius.circular(6)),
                    width: chartData.length == 1 ? 0.3 : 0.6, // 상품이 하나일 때 바 너비 조정
                    spacing: 0.2,
                    dataLabelSettings: DataLabelSettings(
                      isVisible: true,
                      textStyle: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            )
          else
            SizedBox(
              height: 250,
              child: Center(
                child: Text(
                  '데이터가 없습니다',
                  style: TextStyle(
                    fontFamily: 'Pretendard',
                    color: Colors.grey.shade500,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
  /// Secondary Metrics - 보조 지표들
  Widget _buildSecondaryMetrics(Map<String, dynamic> stats, bool isTablet, bool isWide) {
    final List<Widget> cards = [
      _buildDiscountCard(stats),
      _buildPrepaymentCard(stats),
      _buildServiceCard(stats),
      if (widget.selectedSeller == '전체 판매자')
        _buildSellerCard(stats),
    ];

    if (isWide) {
      return Wrap(
        spacing: Dimens.space16,
        runSpacing: Dimens.space16,
        children: cards.map((card) => SizedBox(
          width: (MediaQuery.of(context).size.width - Dimens.space32 - Dimens.space16 * 2) / 3,
          child: card,
        )).toList(),
      );
    } else if (isTablet) {
      return Wrap(
        spacing: Dimens.space16,
        runSpacing: Dimens.space16,
        children: cards.map((card) => SizedBox(
          width: (MediaQuery.of(context).size.width - Dimens.space32 - Dimens.space16) / 2,
          child: card,
        )).toList(),
      );
    } else {
      return Column(
        children: cards.map((card) => Padding(
          padding: const EdgeInsets.only(bottom: Dimens.space16),
          child: card,
        )).toList(),
      );
    }
  }

  /// 할인 분석 카드
  Widget _buildDiscountCard(Map<String, dynamic> stats) {
    final discountAmount = stats['totalSetDiscountAmount'] ?? 0;
    final discountCount = stats['setDiscountCount'] ?? 0;
    final originalTotal = stats['originalTotalBeforeDiscount'] ?? 0;
    final discountRate = originalTotal > 0 ? (discountAmount / originalTotal * 100) : 0.0;

    return _buildMetricCard(
      title: '할인 효과',
      icon: Icons.savings_rounded,
      color: AppColors.success,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            CurrencyUtils.formatCurrency(discountAmount),
            style: const TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: Dimens.space8),
          Text(
            '${discountCount}건 적용 · ${discountRate.toStringAsFixed(1)}% 절약',
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 12,
              color: AppColors.neutral60,
            ),
          ),
          const SizedBox(height: Dimens.space12),
          LinearProgressIndicator(
            value: discountRate / 100,
            backgroundColor: AppColors.success.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.success),
          ),
        ],
      ),
      onTap: () => _showDiscountDialog(stats),
    );
  }

  /// 선입금 현황 카드
  Widget _buildPrepaymentCard(Map<String, dynamic> stats) {
    final receivedAmount = stats['receivedPrepaymentAmount'] ?? 0;
    final pendingAmount = stats['pendingPrepaymentAmount'] ?? 0;
    final totalAmount = receivedAmount + pendingAmount;
    final receivedRate = totalAmount > 0 ? (receivedAmount / totalAmount * 100) : 0.0;

    return _buildMetricCard(
      title: '선입금 현황',
      icon: Icons.account_balance_rounded,
      color: AppColors.info,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            CurrencyUtils.formatCurrency(receivedAmount),
            style: const TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: Dimens.space4),
          Text(
            '수령 완료',
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 12,
              color: AppColors.neutral60,
            ),
          ),
          const SizedBox(height: Dimens.space12),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '수령률',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 10,
                        color: AppColors.neutral60,
                      ),
                    ),
                    Text(
                      '${receivedRate.toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.info,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '미수령',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 10,
                        color: AppColors.neutral60,
                      ),
                    ),
                    Text(
                      CurrencyUtils.formatCurrency(pendingAmount),
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppColors.warning,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      onTap: () => _showPrepaymentDialog(stats),
    );
  }
  /// 서비스 제공 카드
  Widget _buildServiceCard(Map<String, dynamic> stats) {
    final serviceCount = stats['serviceCount'] ?? 0;
    final serviceQuantity = stats['serviceQuantity'] ?? 0;
    final uniqueProducts = stats['uniqueServiceProducts'] ?? 0;

    return _buildMetricCard(
      title: '서비스 제공',
      icon: Icons.volunteer_activism_rounded,
      color: const Color(0xFF7C3AED), // 보라색
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${serviceCount}회',
            style: TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: Dimens.space8),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '총 수량',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 10,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      '${serviceQuantity}개',
                      style: const TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF7C3AED),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '상품 종류',
                      style: TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 10,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      '${uniqueProducts}종',
                      style: const TextStyle(
                        fontFamily: 'Pretendard',
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF7C3AED),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      onTap: () => _showServiceDialog(stats),
    );
  }

  /// 판매자 성과 카드
  Widget _buildSellerCard(Map<String, dynamic> stats) {
    final sellerStats = stats['sellerStats'] as Map<String, Map<String, int>>;
    final topSeller = sellerStats.entries.isNotEmpty
        ? sellerStats.entries.reduce((a, b) =>
            (a.value['revenue'] ?? 0) > (b.value['revenue'] ?? 0) ? a : b)
        : null;

    return _buildMetricCard(
      title: '판매자 성과',
      icon: Icons.people_rounded,
      color: AppColors.accent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (topSeller != null) ...[
            Text(
              topSeller.key,
              style: const TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.onSurface,
              ),
            ),
            const SizedBox(height: Dimens.space4),
            Text(
              'TOP 판매자',
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 10,
                color: AppColors.neutral60,
              ),
            ),
            const SizedBox(height: Dimens.space8),
            Text(
              CurrencyUtils.formatCurrency(topSeller.value['revenue'] ?? 0),
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.accent,
              ),
            ),
          ] else ...[
            const Text(
              '데이터 없음',
              style: TextStyle(
                fontFamily: 'Pretendard',
                fontSize: 16,
                color: AppColors.neutral60,
              ),
            ),
          ],
        ],
      ),
      onTap: () => _showSellerDialog(stats),
    );
  }

  /// 공통 메트릭 카드 빌더
  Widget _buildMetricCard({
    required String title,
    required IconData icon,
    required Color color,
    required Widget child,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(Dimens.radiusL),
      child: Container(
        padding: const EdgeInsets.all(Dimens.space20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(Dimens.radiusL),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(Dimens.space8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(Dimens.radiusS),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                ),
                const SizedBox(width: Dimens.space12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey.shade400,
                  size: 14,
                ),
              ],
            ),
            const SizedBox(height: Dimens.space16),
            child,
          ],
        ),
      ),
    );
  }









  /// 판매자별 성과 섹션 (등수 없이)
  Widget _buildSellerPerformanceSection(Map<String, dynamic> stats) {
    final sellerStats = stats['sellerStats'] as Map<String, Map<String, int>>;

    if (sellerStats.isEmpty) {
      return _buildEmptySection('판매자별 성과');
    }

    final sortedSellers = sellerStats.entries.toList()
      ..sort((a, b) => (b.value['revenue'] ?? 0).compareTo(a.value['revenue'] ?? 0));

    return _buildSectionCard(
      title: '판매자별 성과',
      icon: Icons.people_outlined,
      child: Column(
        children: sortedSellers.map((entry) {
          final revenue = entry.value['revenue'] ?? 0;
          final count = entry.value['count'] ?? 0;
          final averagePerTransaction = count > 0 ? revenue ~/ count : 0;

          return Padding(
            padding: const EdgeInsets.only(bottom: Dimens.space12),
            child: Container(
              padding: const EdgeInsets.all(Dimens.space12),
              decoration: BoxDecoration(
                color: AppColors.neutral10,
                borderRadius: BorderRadius.circular(Dimens.radiusM),
                border: Border.all(
                  color: AppColors.neutral30,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(Dimens.space8),
                        decoration: BoxDecoration(
                          color: AppColors.primarySeed.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(Dimens.radiusS),
                        ),
                        child: Icon(
                          Icons.person,
                          size: 16,
                          color: AppColors.primarySeed,
                        ),
                      ),
                      const SizedBox(width: Dimens.space8),
                      Expanded(
                        child: Text(
                          entry.key,
                          style: const TextStyle(
                            fontFamily: 'Pretendard',
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.onSurface,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: Dimens.space8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '총 매출: ${CurrencyUtils.formatCurrency(revenue)}',
                        style: const TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: 14,
                          color: AppColors.onSurface,
                        ),
                      ),
                      Text(
                        '${count}건',
                        style: TextStyle(
                          fontFamily: 'Pretendard',
                          fontSize: 14,
                          color: AppColors.neutral60,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: Dimens.space4),
                  Text(
                    '평균 거래액: ${CurrencyUtils.formatCurrency(averagePerTransaction)}',
                    style: TextStyle(
                      fontFamily: 'Pretendard',
                      fontSize: 12,
                      color: AppColors.neutral60,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 선입금 현황 섹션
  Widget _buildPrepaymentStatusSection(Map<String, dynamic> stats) {
    return _buildSectionCard(
      title: '선입금 현황',
      icon: Icons.account_balance_outlined,
      child: Column(
        children: [
          _buildStatRow(
            '총 선입금 금액',
            CurrencyUtils.formatCurrency(stats['totalPrepaymentAmount']),
            Icons.savings_outlined,
            AppColors.info,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '수령 완료',
            '${CurrencyUtils.formatCurrency(stats['receivedPrepaymentAmount'])} (${stats['receivedPrepaymentCount']}건)',
            Icons.check_circle_outline,
            AppColors.success,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '미수령',
            '${CurrencyUtils.formatCurrency(stats['pendingPrepaymentAmount'])} (${stats['pendingPrepaymentCount']}건)',
            Icons.pending_outlined,
            AppColors.warning,
          ),
        ],
      ),
    );
  }

  /// 서비스 제공 분석 섹션
  Widget _buildServiceAnalysisSection(Map<String, dynamic> stats) {
    return _buildSectionCard(
      title: '서비스 제공 분석',
      icon: Icons.volunteer_activism_outlined,
      child: Column(
        children: [
          _buildStatRow(
            '서비스 제공 횟수',
            '${stats['serviceCount']}회',
            Icons.volunteer_activism_outlined,
            const Color(0xFF7C3AED),
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '총 서비스 수량',
            '${stats['serviceQuantity']}개',
            Icons.inventory_outlined,
            const Color(0xFF059669),
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '서비스 상품 종류',
            '${stats['uniqueServiceProducts']}종',
            Icons.category_outlined,
            const Color(0xFF2563EB),
          ),
        ],
      ),
    );
  }
  /// 공통 다이얼로그 표시 헬퍼
  Future<void> _showSectionDialog({
    required IconData icon,
    required String title,
    required Color color,
    required Widget content,
  }) async {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth >= 600;

    await showDialog(
      context: context,
      builder: (context) => custom_dialog.DialogTheme.buildModernDialog(
        isCompact: true,
        child: Padding(
          padding: custom_dialog.DialogTheme.getCompactDialogPadding(isTablet),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    custom_dialog.DialogTheme.buildCompactIconContainer(
                      icon: icon,
                      color: color,
                      isTablet: isTablet,
                    ),
                    SizedBox(width: custom_dialog.DialogTheme.getCompactSpacing(isTablet) * 1.5),
                    Expanded(
                      child: Text(
                        title,
                        style: custom_dialog.DialogTheme.titleStyle.copyWith(
                          fontSize: isTablet ? 20.0 : 18.0,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet)),
                content,
                SizedBox(height: custom_dialog.DialogTheme.getCompactSectionSpacing(isTablet) * 1.5),
                Align(
                  alignment: Alignment.centerRight,
                  child: custom_dialog.DialogTheme.buildModernButton(
                    text: '닫기',
                    onPressed: () => Navigator.of(context).pop(),
                    isTablet: isTablet,
                    isPrimary: false,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _showSalesOverviewDialog(Map<String, dynamic> stats) async {
    await _showSectionDialog(
      icon: Icons.dashboard_outlined,
      title: '핵심 지표 요약',
      color: AppColors.primarySeed,
      content: Column(
        children: [
          _buildStatRow(
            '총 매출',
            CurrencyUtils.formatCurrency(stats['totalRevenue']),
            Icons.account_balance_wallet_outlined,
            AppColors.primarySeed,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '총 거래수',
            '${stats['totalTransactions']}건',
            Icons.receipt_long_outlined,
            AppColors.secondary,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '평균 거래액',
            CurrencyUtils.formatCurrency(stats['averageTransaction']),
            Icons.trending_up_outlined,
            AppColors.accent,
          ),
          const SizedBox(height: Dimens.space12),
          _buildStatRow(
            '총 판매량',
            '${stats['totalQuantity']}개',
            Icons.inventory_outlined,
            AppColors.info,
          ),
        ],
      ),
    );
  }

  Future<void> _showDiscountDialog(Map<String, dynamic> stats) async {
    await _showSectionDialog(
      icon: Icons.discount_outlined,
      title: '할인 분석 상세',
      color: AppColors.success,
      content: _buildDiscountAnalysisSection(stats),
    );
  }



  Future<void> _showSellerDialog(Map<String, dynamic> stats) async {
    await _showSectionDialog(
      icon: Icons.people_outlined,
      title: '판매자별 성과 상세',
      color: AppColors.accent,
      content: _buildSellerPerformanceSection(stats),
    );
  }

  Future<void> _showPrepaymentDialog(Map<String, dynamic> stats) async {
    await _showSectionDialog(
      icon: Icons.account_balance_outlined,
      title: '선입금 현황 상세',
      color: AppColors.info,
      content: _buildPrepaymentStatusSection(stats),
    );
  }

  Future<void> _showServiceDialog(Map<String, dynamic> stats) async {
    await _showSectionDialog(
      icon: Icons.volunteer_activism_outlined,
      title: '서비스 제공 상세',
      color: const Color(0xFF7C3AED),
      content: _buildServiceAnalysisSection(stats),
    );
  }



  /// 공통 섹션 카드
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(Dimens.space20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        border: Border.all(
          color: AppColors.neutral60.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.elevation1,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(Dimens.space8),
                decoration: BoxDecoration(
                  color: AppColors.primarySeed.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(Dimens.radiusS),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primarySeed,
                  size: 20,
                ),
              ),
              const SizedBox(width: Dimens.space12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontFamily: 'Pretendard',
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimens.space16),
          child,
        ],
      ),
    );
  }

  /// 빈 섹션 위젯
  Widget _buildEmptySection(String title) {
    return Container(
      width: double.infinity,
      height: 120,
      padding: const EdgeInsets.all(Dimens.space20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(Dimens.radiusL),
        border: Border.all(
          color: AppColors.neutral60.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: 32,
            color: AppColors.neutral40,
          ),
          const SizedBox(height: Dimens.space8),
          Text(
            '$title 데이터가 없습니다',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontFamily: 'Pretendard',
              color: AppColors.neutral60,
            ),
          ),
        ],
      ),
    );
  }

  /// 통계 행 위젯
  Widget _buildStatRow(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(Dimens.space6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(Dimens.radiusS),
          ),
          child: Icon(
            icon,
            size: 16,
            color: color,
          ),
        ),
        const SizedBox(width: Dimens.space12),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(
              fontFamily: 'Pretendard',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.onSurface,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontFamily: 'Pretendard',
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }


}
