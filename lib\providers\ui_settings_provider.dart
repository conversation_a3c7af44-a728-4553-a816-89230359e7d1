/// Blue Booth Manager - UI 설정 Provider
///
/// UI 설정 상태 관리를 담당하는 Provider 클래스입니다.
/// - 스마트폰/타블렛 별도 열 수 설정 관리
/// - 실시간 동기화 지원
/// - 디바이스 타입별 설정 자동 적용
///
/// 주요 기능:
/// - UI 설정 로드/저장
/// - 디바이스별 열 수 설정
/// - 실시간 Firebase 동기화
/// - 설정 변경 알림
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ui_settings.dart';
import '../repositories/ui_settings_repository.dart';
import '../services/database_service.dart';
import '../services/data_sync_service.dart';
import '../utils/logger_utils.dart';


/// UI 설정 상태
class UISettingsState {
  final UISettings settings;
  final bool isLoading;
  final String? error;

  const UISettingsState({
    required this.settings,
    this.isLoading = false,
    this.error,
  });

  UISettingsState copyWith({
    UISettings? settings,
    bool? isLoading,
    String? error,
  }) {
    return UISettingsState(
      settings: settings ?? this.settings,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// UI 설정 Repository Provider
final uiSettingsRepositoryProvider = Provider<UISettingsRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return UISettingsRepository(databaseService);
});

/// UI 설정 Notifier
class UISettingsNotifier extends StateNotifier<UISettingsState> {
  static const String _tag = 'UISettingsNotifier';
  
  final UISettingsRepository repository;
  final DataSyncService syncService;

  UISettingsNotifier(this.repository, this.syncService) 
      : super(const UISettingsState(settings: UISettings())) {
    _loadSettings();
  }

  /// UI 설정 로드
  Future<void> _loadSettings() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final settings = await repository.getUISettings() ?? const UISettings();
      
      state = state.copyWith(
        settings: settings,
        isLoading: false,
      );
      
      LoggerUtils.logInfo('UI 설정 로드 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('UI 설정 로드 실패', tag: _tag, error: e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 디바이스별 열 수 업데이트
  Future<void> updateColumnsForDevice(bool isTablet, int columns) async {
    try {
      LoggerUtils.logInfo('디바이스별 열 수 업데이트: isTablet=$isTablet, columns=$columns', tag: _tag);
      
      final updatedSettings = await repository.updateColumnsForDevice(isTablet, columns);
      
      state = state.copyWith(settings: updatedSettings);
      
      // Firebase 동기화 (디바이스 타입은 설정에서 추론)
      await _syncToFirebase(updatedSettings, isTablet);

      LoggerUtils.logInfo('디바이스별 열 수 업데이트 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('디바이스별 열 수 업데이트 실패', tag: _tag, error: e);
      state = state.copyWith(error: e.toString());
    }
  }

  /// 세로모드 열 수 업데이트
  Future<void> updatePortraitColumns(int columns) async {
    try {
      final currentSettings = state.settings;
      final updatedSettings = currentSettings.copyWith(
        portraitColumns: columns,
        updatedAt: DateTime.now(),
      );

      await updateSettings(updatedSettings);
      LoggerUtils.logInfo('세로모드 열 수 업데이트 완료: $columns', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('세로모드 열 수 업데이트 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 가로모드 열 수 업데이트
  Future<void> updateLandscapeColumns(int columns) async {
    try {
      final currentSettings = state.settings;
      final updatedSettings = currentSettings.copyWith(
        landscapeColumns: columns,
        updatedAt: DateTime.now(),
      );

      await updateSettings(updatedSettings);
      LoggerUtils.logInfo('가로모드 열 수 업데이트 완료: $columns', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('가로모드 열 수 업데이트 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 현재 디바이스에 맞는 열 수 반환 (세로모드 기본값)
  int getCurrentDeviceColumns() {
    // 기본적으로 세로모드 열 수 반환
    return state.settings.portraitColumns;
  }

  /// 방향별 열 수 조회
  int getColumnsForOrientation(bool isLandscape) {
    return isLandscape ? state.settings.landscapeColumns : state.settings.portraitColumns;
  }

  /// UI 설정 전체 업데이트
  Future<void> updateSettings(UISettings settings) async {
    try {
      LoggerUtils.logInfo('UI 설정 전체 업데이트', tag: _tag);
      
      final updatedSettings = await repository.saveUISettings(settings);
      
      state = state.copyWith(settings: updatedSettings);
      
      // Firebase 동기화 (기본값으로 스마트폰 사용)
      await _syncToFirebase(updatedSettings, false);

      LoggerUtils.logInfo('UI 설정 전체 업데이트 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('UI 설정 전체 업데이트 실패', tag: _tag, error: e);
      state = state.copyWith(error: e.toString());
    }
  }

  /// 기본값으로 초기화
  Future<void> resetToDefault() async {
    try {
      LoggerUtils.logInfo('UI 설정 기본값으로 초기화', tag: _tag);
      
      const defaultSettings = UISettings();
      final updatedSettings = await repository.saveUISettings(defaultSettings);
      
      state = state.copyWith(settings: updatedSettings);
      
      // Firebase 동기화 (기본값으로 스마트폰 사용)
      await _syncToFirebase(updatedSettings, false);

      LoggerUtils.logInfo('UI 설정 기본값으로 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('UI 설정 기본값으로 초기화 실패', tag: _tag, error: e);
      state = state.copyWith(error: e.toString());
    }
  }

  /// Firebase 동기화
  Future<void> _syncToFirebase(UISettings settings, bool isTablet) async {
    try {
      await syncService.uploadSingleUISettings(settings, isTablet);
      LoggerUtils.logDebug('UI 설정 Firebase 동기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logWarning('UI 설정 Firebase 동기화 실패 (계속 진행)', tag: _tag, error: e);
      // 동기화 실패는 치명적이지 않으므로 에러 상태로 설정하지 않음
    }
  }

  /// 에러 상태 클리어
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// 설정 새로고침
  Future<void> refresh() async {
    await _loadSettings();
  }
}

/// UI 설정 Provider
final uiSettingsNotifierProvider = StateNotifierProvider<UISettingsNotifier, UISettingsState>((ref) {
  final repository = ref.watch(uiSettingsRepositoryProvider);
  final databaseService = ref.watch(databaseServiceProvider);
  final syncService = DataSyncService(databaseService);
  return UISettingsNotifier(repository, syncService);
});

/// 현재 방향별 열 수 Provider (context 필요)
final currentOrientationColumnsProvider = Provider.family<int, bool>((ref, isLandscape) {
  return ref.read(uiSettingsNotifierProvider.notifier).getColumnsForOrientation(isLandscape);
});

/// 세로모드 열 수 Provider
final portraitColumnsProvider = Provider<int>((ref) {
  final uiSettings = ref.watch(uiSettingsNotifierProvider);
  return uiSettings.settings.portraitColumns;
});

/// 가로모드 열 수 Provider
final landscapeColumnsProvider = Provider<int>((ref) {
  final uiSettings = ref.watch(uiSettingsNotifierProvider);
  return uiSettings.settings.landscapeColumns;
});
